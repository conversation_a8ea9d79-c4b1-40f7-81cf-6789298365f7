import asyncio
import pytest
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from scheduler.other.balance_update_scheduler import execute_task


class TestBalanceUpdateScheduler:
    """测试余额更新调度器"""
    
    @pytest.fixture
    def mock_user_account(self):
        """模拟用户账户"""
        account = MagicMock()
        account.id = "account_123"
        account.user_id = "user_456"
        account.balance = 100.0
        account.save = AsyncMock()
        return account
    
    @pytest.fixture
    def mock_recharge_record(self):
        """模拟充值记录"""
        record = MagicMock()
        record.amount = 200.0
        record.status = "成功"
        return record
    
    @pytest.fixture
    def mock_consumption_record(self):
        """模拟消费记录"""
        record = MagicMock()
        record.amount = 50.0
        return record

    @patch('scheduler.other.balance_update_scheduler.CostUserAccount')
    @patch('scheduler.other.balance_update_scheduler.CostRechargeRecord')
    @patch('scheduler.other.balance_update_scheduler.CostConsumptionRecord')
    @patch('scheduler.other.balance_update_scheduler.olog')
    async def test_execute_task_success(self, mock_olog, mock_consumption_model, 
                                       mock_recharge_model, mock_account_model,
                                       mock_user_account, mock_recharge_record, 
                                       mock_consumption_record):
        """测试成功执行余额更新任务"""
        
        # 设置模拟数据
        mock_account_model.find_all.return_value.to_list = AsyncMock(return_value=[mock_user_account])
        mock_recharge_model.find.return_value.to_list = AsyncMock(return_value=[mock_recharge_record])
        mock_consumption_model.find.return_value.to_list = AsyncMock(return_value=[mock_consumption_record])
        
        # 执行任务
        await execute_task()
        
        # 验证日志记录
        mock_olog.info.assert_any_call("开始执行用户余额更新任务")
        mock_olog.info.assert_any_call("余额更新任务完成，共更新 1 个账户")
        
        # 验证账户余额被更新
        assert mock_user_account.balance == 150.0  # 200 - 50 = 150
        mock_user_account.save.assert_called_once()

    @patch('scheduler.other.balance_update_scheduler.CostUserAccount')
    @patch('scheduler.other.balance_update_scheduler.olog')
    async def test_execute_task_no_accounts(self, mock_olog, mock_account_model):
        """测试没有用户账户的情况"""
        
        # 设置空的用户账户列表
        mock_account_model.find_all.return_value.to_list = AsyncMock(return_value=[])
        
        # 执行任务
        await execute_task()
        
        # 验证日志记录
        mock_olog.info.assert_any_call("开始执行用户余额更新任务")
        mock_olog.info.assert_any_call("未找到任何用户账户，跳过余额更新")

    @patch('scheduler.other.balance_update_scheduler.CostUserAccount')
    @patch('scheduler.other.balance_update_scheduler.CostRechargeRecord')
    @patch('scheduler.other.balance_update_scheduler.CostConsumptionRecord')
    @patch('scheduler.other.balance_update_scheduler.olog')
    async def test_execute_task_no_user_id(self, mock_olog, mock_consumption_model,
                                          mock_recharge_model, mock_account_model):
        """测试用户账户缺少user_id的情况"""
        
        # 创建没有user_id的账户
        account_without_user_id = MagicMock()
        account_without_user_id.id = "account_123"
        account_without_user_id.user_id = None
        
        mock_account_model.find_all.return_value.to_list = AsyncMock(return_value=[account_without_user_id])
        
        # 执行任务
        await execute_task()
        
        # 验证警告日志
        mock_olog.warning.assert_called_with("账户 account_123 缺少user_id，跳过")
        mock_olog.info.assert_any_call("余额更新任务完成，共更新 0 个账户")

    @patch('scheduler.other.balance_update_scheduler.CostUserAccount')
    @patch('scheduler.other.balance_update_scheduler.CostRechargeRecord')
    @patch('scheduler.other.balance_update_scheduler.CostConsumptionRecord')
    @patch('scheduler.other.balance_update_scheduler.olog')
    async def test_execute_task_no_balance_change(self, mock_olog, mock_consumption_model,
                                                 mock_recharge_model, mock_account_model,
                                                 mock_user_account, mock_recharge_record,
                                                 mock_consumption_record):
        """测试余额没有变化的情况"""
        
        # 设置当前余额与计算余额相同
        mock_user_account.balance = 150.0  # 200 - 50 = 150
        
        mock_account_model.find_all.return_value.to_list = AsyncMock(return_value=[mock_user_account])
        mock_recharge_model.find.return_value.to_list = AsyncMock(return_value=[mock_recharge_record])
        mock_consumption_model.find.return_value.to_list = AsyncMock(return_value=[mock_consumption_record])
        
        # 执行任务
        await execute_task()
        
        # 验证账户没有被保存（余额没有变化）
        mock_user_account.save.assert_not_called()
        mock_olog.info.assert_any_call("余额更新任务完成，共更新 0 个账户")


def run_simple_test():
    """简单运行测试的函数"""
    print("开始运行余额更新调度器测试...")
    
    # 运行所有测试
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    # 简单运行测试
    run_simple_test()
